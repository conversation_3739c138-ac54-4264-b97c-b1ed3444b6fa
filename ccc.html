<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>..:: $(identity) ::.. Hotspot</title>

    <!-- TailwindCSS for modern styling -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts (Cairo) for beautiful Arabic text -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Custom CSS for the page -->
    <style>
        /* Base styles and animations */
        body {
            font-family: 'Cairo', sans-serif;
            overflow: hidden;
        }

        /* Keyframes for animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes zoomIn {
            from { transform: scale(0.9); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }
        @keyframes slideUp {
            from { transform: translateY(100%); }
            to { transform: translateY(0); }
        }

        /* --- Smartphone Frame --- */
        .phone-frame {
            animation: fadeIn 1s ease-out;
            background: #1a1a1a;
            border-radius: 50px;
            padding: 12px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5), 0 0 0 8px #111;
            transform: scale(0.95) rotateX(10deg) rotateY(-5deg);
            transform-style: preserve-3d;
            transition: transform 0.5s ease;
        }
        .phone-frame:hover {
            transform: scale(1) rotateX(0) rotateY(0);
        }

        .phone-screen {
            background: #f0f2f5;
            border-radius: 40px;
            overflow: hidden;
            position: relative;
            aspect-ratio: 9 / 19.5; /* Modern phone aspect ratio */
        }
        
        .phone-notch {
            position: absolute;
            top: 12px;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 25px;
            background: #1a1a1a;
            border-radius: 0 0 15px 15px;
            z-index: 10;
        }

        /* --- Ad Slider Styles --- */
        .ad-slider .slide {
            transition: opacity 1.5s ease-in-out;
        }

        /* --- Modal Styles --- */
        .modal-backdrop {
            transition: opacity 0.3s ease;
        }
        .modal-content {
            animation: zoomIn 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
            transition: transform 0.3s ease;
        }
        
        /* --- Custom Toggle Switch --- */
        .toggle-checkbox:checked + .toggle-label {
            background: #34d399; /* Green when on */
        }
        .toggle-checkbox:checked + .toggle-label::after {
            transform: translateX(100%);
            border-color: #34d399;
        }
        .toggle-label::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 9999px;
            transition: transform 0.3s ease;
        }
        
        /* Hide scrollbar */
        .no-scrollbar::-webkit-scrollbar { display: none; }
        .no-scrollbar { -ms-overflow-style: none; scrollbar-width: none; }
    </style>
</head>

<body class="bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 flex items-center justify-center min-h-screen">
    
    <!-- MikroTik Login Form (hidden, used for final submission) -->
    $(if chap-id)
    <script type="text/javascript" src="/md5.js"></script>
    <form name="sendin" action="$(link-login-only)" method="post" style="display:none;">
        <input type="hidden" name="username" />
        <input type="hidden" name="password" />
        <input type="hidden" name="dst" value="$(link-orig)" />
        <input type="hidden" name="popup" value="true" />
    </form>
    $(endif)

    <form name="login" action="$(link-login-only)" method="post" style="display:none;" $(if chap-id) onSubmit="return doLogin()" $(endif)>
        <input type="hidden" name="dst" value="$(link-orig)" />
        <input type="hidden" name="popup" value="true" />
        <input type="text" name="username" />
        <input type="password" name="password" />
    </form>
    
    <!-- Phone Frame Container -->
    <div class="phone-frame w-full max-w-sm">
        <div class="phone-screen w-full h-full">
            <div class="phone-notch"></div>
            
            <!-- Main Content Area -->
            <div id="content-area" class="w-full h-full flex flex-col">
                
                <!-- 1. Ad Slider Section -->
                <div id="ad-slider-section" class="relative w-full h-full flex-shrink-0">
                    <div class="ad-slider absolute inset-0">
                        <!-- Slide Images -->
                        <div class="slide absolute inset-0 bg-cover bg-center opacity-0" style="background-image: url('https://placehold.co/400x800/3498db/ffffff?text=Your+Ad+Here');"></div>
                        <div class="slide absolute inset-0 bg-cover bg-center opacity-0" style="background-image: url('https://placehold.co/400x800/e74c3c/ffffff?text=اعلان+شركتك');"></div>
                        <div class="slide absolute inset-0 bg-cover bg-center opacity-0" style="background-image: url('https://placehold.co/400x800/2ecc71/ffffff?text=عروض+خاصة');"></div>
                    </div>
                    <div class="absolute bottom-10 left-0 right-0 p-4 text-center">
                        <div class="bg-black bg-opacity-50 text-white p-4 rounded-2xl">
                            <h2 class="text-xl font-bold network-name">أهلاً بكم في شبكة $(identity)</h2>
                            <p class="text-sm">جاري تحميل صفحة تسجيل الدخول...</p>
                        </div>
                    </div>
                </div>

                <!-- 2. Login Section -->
                <div id="login-section" class="w-full h-full bg-white flex-shrink-0 p-6 flex flex-col justify-between hidden">
                    <!-- Header -->
                    <div class="text-center pt-8">
                        <i class="fas fa-wifi text-5xl text-indigo-600"></i>
                        <h1 class="text-3xl font-bold text-gray-800 mt-4 network-name">شبكة $(identity)</h1>
                        <p class="text-gray-500 text-sm" id="clock">--:--:--</p>
                    </div>

                    <!-- Login Form -->
                    <div class="w-full">
                        <div class="relative mb-4">
                            <span class="absolute inset-y-0 left-0 flex items-center pl-4 text-gray-400">
                                <i class="fas fa-ticket-alt"></i>
                            </span>
                            <input type="text" id="username-input" placeholder="ادخل رقم الكرت" class="w-full pl-12 pr-4 py-3.5 rounded-xl border-2 border-gray-200 bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:border-transparent transition text-center" required>
                        </div>
                        
                        <button id="login-btn" class="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-4 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                            تسجيل الدخول
                        </button>
                    </div>
                    
                    <!-- Saved Cards & Error Message -->
                    <div class="text-center">
                        <button id="show-saved-cards-btn" class="text-indigo-600 hover:underline">
                            <i class="fas fa-save"></i> الكروت المحفوظة
                        </button>
                        $(if error)
                        <div id="error-message" class="bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded-lg mt-4 text-sm" role="alert">
                            <strong class="font-bold">خطأ:</strong>
                            <span class="block sm:inline">$(error)</span>
                        </div>
                        $(endif)
                    </div>

                    <!-- Footer -->
                    <div class="text-center text-xs text-gray-400 pb-2">
                        تصميم وتطوير Gemini
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modals (shared backdrop) -->
    <div id="modal-backdrop" class="fixed inset-0 bg-black bg-opacity-60 flex items-end justify-center z-50 opacity-0 pointer-events-none">
        <!-- Speed & Options Modal -->
        <div id="options-modal-content" class="modal-content w-full max-w-md bg-white rounded-t-3xl shadow-lg p-6 hidden" style="animation: slideUp 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);">
             <h2 class="text-2xl font-bold text-center text-gray-800 mb-6">إعدادات الاتصال</h2>
            
            <!-- Speed Selection -->
            <div class="mb-6">
                <label class="block text-gray-700 text-sm font-bold mb-2"><i class="fas fa-tachometer-alt"></i> اختر السرعة المناسبة:</label>
                <select id="speed-select" class="block appearance-none w-full bg-gray-100 border border-gray-200 text-gray-700 py-3 px-4 pr-8 rounded-lg leading-tight focus:outline-none focus:bg-white focus:border-indigo-500">
                    <option value="speed_economic|">سرعة اقتصادية</option>
                    <option value="speed_normal|">سرعة عادية</option>
                    <option value="speed_default|" selected>سرعة متوسطة</option>
                    <option value="speed_high|">سرعة عالية</option>
                    <option value="speed_very|">سرعة عالية جدًا</option>
                </select>
            </div>
            
            <!-- Update Toggle -->
            <div class="flex items-center justify-between bg-gray-100 p-3 rounded-lg mb-8">
                <label for="update-toggle" class="text-gray-700 font-bold"><i class="fas fa-sync-alt"></i> تحديثات النظام والتطبيقات</label>
                <div class="relative inline-block w-12 mr-2 align-middle select-none transition duration-200 ease-in">
                    <input type="checkbox" name="update-toggle" id="update-toggle" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" checked/>
                    <label for="update-toggle" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer"></label>
                </div>
            </div>
            
            <button id="confirm-login-btn" class="w-full bg-gradient-to-r from-green-500 to-teal-500 text-white py-3.5 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300">
                تأكيد والاتصال
            </button>
        </div>
        
        <!-- Saved Cards Modal -->
        <div id="saved-cards-modal-content" class="modal-content w-full max-w-md bg-white rounded-t-3xl shadow-lg p-6 hidden" style="animation: slideUp 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-2xl font-bold text-gray-800">الكروت المحفوظة</h2>
                <button id="close-saved-cards" class="text-gray-500 hover:text-red-500 text-2xl">&times;</button>
            </div>
            <div id="saved-cards-list" class="space-y-3 max-h-60 overflow-y-auto no-scrollbar">
                <!-- Saved cards will be populated here by JS -->
            </div>
        </div>
    </div>


    <script type="text/javascript">
    // --- الشرح: هذا الجزء من الكود مسؤول عن التوافق مع نظام الميكروتك ---
    // This script block handles MikroTik-specific login functions.
    /* $(if chap-id) */
    function doLogin() {
        // This function is for CHAP-MD5 authentication.
        // It calculates the MD5 hash for the password before sending.
        // Note: For a username-only login, this part might be bypassed
        // or a dummy password might be used depending on the router setup.
        document.sendin.username.value = document.login.username.value;
        document.sendin.password.value = hexMD5('$(chap-id)' + document.login.password.value + '$(chap-challenge)');
        document.sendin.submit();
        return false;
    }
    /* $(endif) */
    
    // --- الشرح: هذا هو الكود الرئيسي لجميع وظائف الصفحة التفاعلية ---
    // Main script for all page functionality.
    document.addEventListener('DOMContentLoaded', () => {
    
        // --- Elements ---
        const adSliderSection = document.getElementById('ad-slider-section');
        const loginSection = document.getElementById('login-section');
        const clockElement = document.getElementById('clock');
        const loginBtn = document.getElementById('login-btn');
        const confirmLoginBtn = document.getElementById('confirm-login-btn');
        const usernameInput = document.getElementById('username-input');
        const speedSelect = document.getElementById('speed-select');
        const updateToggle = document.getElementById('update-toggle');

        // Modals
        const modalBackdrop = document.getElementById('modal-backdrop');
        const optionsModal = document.getElementById('options-modal-content');
        const savedCardsModal = document.getElementById('saved-cards-modal-content');
        const showSavedCardsBtn = document.getElementById('show-saved-cards-btn');
        const closeSavedCardsBtn = document.getElementById('close-saved-cards');
        const savedCardsList = document.getElementById('saved-cards-list');

        // --- Ad Slider Logic ---
        const slides = adSliderSection.querySelectorAll('.slide');
        let currentSlide = 0;
        
        function showNextSlide() {
            slides[currentSlide].style.opacity = '0';
            currentSlide = (currentSlide + 1) % slides.length;
            slides[currentSlide].style.opacity = '1';
        }

        // --- Page Initialization ---
        function init() {
            // Start Ad Slider
            slides[0].style.opacity = '1';
            setInterval(showNextSlide, 4000); // Change slide every 4 seconds

            // Transition from Ads to Login page
            setTimeout(() => {
                adSliderSection.style.display = 'none';
                loginSection.classList.remove('hidden');
                loginSection.style.animation = 'fadeIn 1s ease-in-out';
            }, 5000); // Show login after 5 seconds

            // Start Clock
            updateClock();
            setInterval(updateClock, 1000);

            // Load saved cards on init
            loadSavedCards();
        }

        // --- Clock Function ---
        function updateClock() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
            clockElement.textContent = timeString;
        }
        
        // --- Modal Control ---
        function openModal(modalContent) {
            modalBackdrop.classList.remove('opacity-0', 'pointer-events-none');
            modalContent.classList.remove('hidden');
        }

        function closeModal() {
            modalBackdrop.classList.add('opacity-0', 'pointer-events-none');
            optionsModal.classList.add('hidden');
            savedCardsModal.classList.add('hidden');
        }

        modalBackdrop.addEventListener('click', (e) => {
            if (e.target === modalBackdrop) {
                closeModal();
            }
        });
        
        // --- Login Flow ---
        loginBtn.addEventListener('click', () => {
            const username = usernameInput.value.trim();
            if (!username) {
                // Show a simple visual warning if username is empty
                usernameInput.style.borderColor = '#ef4444';
                setTimeout(() => { usernameInput.style.borderColor = ''; }, 2000);
                return;
            }
            openModal(optionsModal);
        });

        confirmLoginBtn.addEventListener('click', () => {
            const username = usernameInput.value.trim();
            const speed = speedSelect.value;
            const updates = updateToggle.checked ? 'updateON' : 'updateOFF';
            
            // Format username with options: speed|updates|other_options
            // Example: 12345speed_high|updateOFF|all
            const finalUsername = `${username}${speed}${updates}|all|`;
            
            // Save the card (base number only)
            saveCard(username);
            
            // Submit to MikroTik
            // الشرح: يتم هنا إرسال البيانات إلى راوتر الميكروتك لتسجيل الدخول
            const loginForm = document.forms.login;
            loginForm.username.value = finalUsername;
            loginForm.password.value = "123"; // Using a dummy password for username-only login
            loginForm.submit();
        });

        // --- Saved Cards Logic ---
        const MAX_SAVED_CARDS = 5;

        function getSavedCards() {
            return JSON.parse(localStorage.getItem('savedHotspotCards')) || [];
        }

        function saveCard(username) {
            let cards = getSavedCards();
            // Remove if already exists to move it to the top
            cards = cards.filter(card => card !== username);
            // Add to the beginning of the array
            cards.unshift(username);
            // Keep only the last 5 cards
            if (cards.length > MAX_SAVED_CARDS) {
                cards = cards.slice(0, MAX_SAVED_CARDS);
            }
            localStorage.setItem('savedHotspotCards', JSON.stringify(cards));
        }

        function loadSavedCards() {
            const cards = getSavedCards();
            savedCardsList.innerHTML = ''; // Clear existing list
            if (cards.length === 0) {
                savedCardsList.innerHTML = `<p class="text-center text-gray-500">لا توجد كروت محفوظة.</p>`;
                return;
            }
            cards.forEach(card => {
                const cardElement = document.createElement('div');
                cardElement.className = 'flex items-center justify-between bg-gray-100 p-3 rounded-lg';
                cardElement.innerHTML = `
                    <span class="font-semibold text-gray-700">${card}</span>
                    <div>
                        <button data-username="${card}" class="select-card-btn bg-indigo-500 text-white px-3 py-1 text-sm rounded-md hover:bg-indigo-600 transition">اختيار</button>
                        <button data-username="${card}" class="delete-card-btn text-red-500 hover:text-red-700 ml-2"><i class="fas fa-trash-alt"></i></button>
                    </div>
                `;
                savedCardsList.appendChild(cardElement);
            });
        }
        
        // Event delegation for dynamically created buttons
        savedCardsList.addEventListener('click', (e) => {
            const target = e.target.closest('button');
            if (!target) return;
            
            const username = target.dataset.username;

            if (target.classList.contains('select-card-btn')) {
                usernameInput.value = username;
                closeModal();
            } else if (target.classList.contains('delete-card-btn')) {
                let cards = getSavedCards();
                cards = cards.filter(card => card !== username);
                localStorage.setItem('savedHotspotCards', JSON.stringify(cards));
                loadSavedCards(); // Refresh the list
            }
        });

        showSavedCardsBtn.addEventListener('click', () => {
            loadSavedCards(); // Ensure the list is up-to-date
            openModal(savedCardsModal);
        });

        closeSavedCardsBtn.addEventListener('click', closeModal);

        // Start the application
        init();
    });
    </script>
</body>
</html>

