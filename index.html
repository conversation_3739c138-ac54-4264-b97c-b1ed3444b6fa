<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MikroTik Hotspot - تسجيل الدخول</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        /* Animated Background */
        .bg-animated {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
        }
        
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        /* Floating particles */
        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        /* Phone-like container */
        .phone-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            max-width: 400px;
            margin: 0 auto;
            overflow: hidden;
            position: relative;
            transition: all 0.3s ease;
        }
        
        /* Notch design */
        .notch {
            width: 150px;
            height: 25px;
            background: #000;
            border-radius: 0 0 15px 15px;
            margin: 0 auto;
            position: relative;
        }
        
        .notch::before {
            content: '';
            position: absolute;
            top: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 50px;
            height: 4px;
            background: #333;
            border-radius: 2px;
        }
        
        /* Animated ads banner */
        .ads-banner {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }
        
        .ads-banner::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: shine 3s infinite;
        }
        
        @keyframes shine {
            0% { transform: rotate(45deg) translateX(-100%); }
            100% { transform: rotate(45deg) translateX(100%); }
        }
        
        /* Form styling */
        .form-input {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 15px;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            width: 100%;
            box-sizing: border-box;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
            transform: translateY(-2px);
        }
        
        /* Button styling */
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            width: 100%;
            box-sizing: border-box;
        }
        
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        
        .btn-primary:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn-primary:hover::before {
            left: 100%;
        }
        
        /* Modal styling */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }
        
        .modal.show {
            display: flex;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .modal-content {
            background: white;
            border-radius: 20px;
            padding: 30px;
            max-width: 90%;
            width: 400px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            animation: slideUp 0.3s ease;
        }
        
        @keyframes slideUp {
            from { transform: translateY(50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        /* Speed selection cards */
        .speed-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid transparent;
            border-radius: 15px;
            padding: 20px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .speed-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .speed-card.selected {
            border-color: #667eea;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .speed-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }
        
        .speed-card:hover::before {
            left: 100%;
        }
        
        /* Toggle switch */
        .toggle-switch {
            width: 60px;
            height: 30px;
            background: #ccc;
            border-radius: 15px;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .toggle-switch.active {
            background: #667eea;
        }
        
        .toggle-thumb {
            width: 26px;
            height: 26px;
            background: white;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        
        .toggle-switch.active .toggle-thumb {
            transform: translateX(30px);
        }
        
        /* Saved cards styling */
        .saved-card {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 10px;
            padding: 10px 15px;
            margin: 5px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .saved-card:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateX(-5px);
        }
        
        /* Status page styling */
        .status-page {
            display: none;
            animation: slideIn 0.5s ease;
        }
        
        @keyframes slideIn {
            from { transform: translateX(100%); }
            to { transform: translateX(0); }
        }
        
        /* Page transition effects */
        .fade-out {
            opacity: 0;
            transform: translateX(-100%);
        }
        
        .fade-in {
            opacity: 1;
            transform: translateX(0);
        }
        
        /* Responsive design */
        @media (max-width: 480px) {
            .phone-container {
                margin: 10px;
                border-radius: 20px;
                max-width: calc(100% - 20px);
            }
            
            .modal-content {
                margin: 20px;
                padding: 20px;
                width: calc(100% - 40px);
            }
            
            .notch {
                width: 120px;
                height: 20px;
            }
        }
        
        /* Loading animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* Notification styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            padding: 15px 20px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            max-width: 300px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.success { background: #10b981; }
        .notification.error { background: #ef4444; }
        .notification.warning { background: #f59e0b; }
        .notification.info { background: #3b82f6; }
        
        /* Fix for RTL layout */
        [dir="rtl"] .toggle-switch.active .toggle-thumb {
            transform: translateX(-30px);
        }
        
        [dir="rtl"] .saved-card:hover {
            transform: translateX(5px);
        }
        
        /* Utility classes */
        .rounded-15 { border-radius: 15px; }
        .rounded-10 { border-radius: 10px; }
        
        /* Fix overlapping issues */
        .phone-container > * {
            position: relative;
            z-index: 1;
        }
        
        /* Ensure proper spacing */
        .space-y-6 > * + * {
            margin-top: 1.5rem;
        }
        
        .space-y-3 > * + * {
            margin-top: 0.75rem;
        }
        
        /* Fix button alignment */
        .flex.gap-3 {
            display: flex;
            gap: 0.75rem;
        }
        
        .flex-1 {
            flex: 1;
        }
    </style>
</head>
<body>
    <!-- Animated Background -->
    <div class="bg-animated"></div>
    
    <!-- Floating Particles -->
    <div class="particle" style="top: 10%; left: 10%; width: 10px; height: 10px; animation-delay: 0s;"></div>
    <div class="particle" style="top: 20%; left: 80%; width: 15px; height: 15px; animation-delay: 1s;"></div>
    <div class="particle" style="top: 60%; left: 20%; width: 8px; height: 8px; animation-delay: 2s;"></div>
    <div class="particle" style="top: 80%; left: 70%; width: 12px; height: 12px; animation-delay: 3s;"></div>
    
    <div class="min-h-screen flex items-center justify-center p-4">
        <!-- Login Page -->
        <div id="loginPage" class="phone-container">
            <!-- Notch -->
            <div class="notch"></div>
            
            <!-- Animated Ads Banner -->
            <div class="ads-banner">
                <div id="adsText">🚀 اتصال سريع وآمن - استمتع بتجربة إنترنت مميزة!</div>
            </div>
            
            <!-- Main Content -->
            <div class="p-8">
                <!-- Logo and Title -->
                <div class="text-center mb-8">
                    <div class="text-6xl mb-4">📶</div>
                    <h1 class="text-2xl font-bold text-gray-800 mb-2">MikroTik Hotspot</h1>
                    <p class="text-gray-600">مرحباً بك في شبكة الإنترنت السريعة</p>
                </div>
                
                <!-- Login Form -->
                <form id="loginForm" class="space-y-6">
                    <div>
                        <label class="block text-gray-700 font-semibold mb-2">اسم المستخدم</label>
                        <input type="text" id="username" class="form-input" placeholder="أدخل اسم المستخدم" required>
                    </div>
                    
                    <button type="submit" class="btn-primary">
                        <span id="loginText">تسجيل الدخول</span>
                        <span id="loginLoading" class="loading" style="display: none;"></span>
                    </button>
                </form>
                
                <!-- Saved Cards Button -->
                <button id="showSavedCards" class="w-full mt-4 p-3 bg-gray-100 text-gray-700 rounded-15 font-semibold hover:bg-gray-200 transition-all duration-300">
                    📋 عرض الكروت المحفوظة (<span id="savedCount">0</span>)
                </button>
                
                <!-- Footer -->
                <div class="text-center mt-8 text-sm text-gray-500">
                    <p>© 2024 MikroTik Hotspot System</p>
                    <p>تم التطوير بواسطة فريق التقنية المتقدمة</p>
                </div>
            </div>
        </div>
        
        <!-- Status Page -->
        <div id="statusPage" class="phone-container status-page">
            <!-- Notch -->
            <div class="notch"></div>
            
            <!-- Status Header -->
            <div class="bg-green-500 text-white p-4 text-center">
                <div class="text-2xl mb-2">✅</div>
                <h2 class="text-xl font-bold">متصل بنجاح</h2>
                <p class="text-sm opacity-90">مرحباً <span id="connectedUser"></span></p>
            </div>
            
            <!-- Status Content -->
            <div class="p-6 space-y-6">
                <!-- Connection Info -->
                <div class="bg-blue-50 rounded-15 p-4">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="font-bold text-gray-800">📊 معلومات الاتصال</h3>
                        <div class="flex items-center">
                            <span class="text-xs text-gray-500 ml-2">حالة الاتصال</span>
                            <div id="connectionIndicator" class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                        </div>
                    </div>
                    <div class="space-y-3 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">السرعة الحالية:</span>
                            <span class="font-semibold text-blue-600" id="currentSpeed">سرعة عادية</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">جودة الشبكة:</span>
                            <span class="font-semibold text-green-600" id="networkQuality">ممتاز</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">وقت الاتصال:</span>
                            <span class="font-semibold" id="connectionTime">00:00:00</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">البيانات المستخدمة:</span>
                            <span class="font-semibold" id="dataUsed">0 MB</span>
                        </div>
                        
                        <!-- Bandwidth Usage Chart -->
                        <div class="mt-4">
                            <div class="flex justify-between text-xs text-gray-500 mb-1">
                                <span>استخدام النطاق الترددي</span>
                                <span class="usage-percent">0%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div id="bandwidthChart" class="bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-1000" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Speed Control -->
                <div class="bg-purple-50 rounded-15 p-4">
                    <h3 class="font-bold text-gray-800 mb-3">⚡ تغيير السرعة</h3>
                    <button id="changeSpeedBtn" class="w-full bg-purple-500 text-white p-3 rounded-10 font-semibold hover:bg-purple-600 transition-all duration-300">
                        تغيير السرعة
                    </button>
                </div>
                
                <!-- Updates Control -->
                <div class="bg-orange-50 rounded-15 p-4">
                    <h3 class="font-bold text-gray-800 mb-3">📱 إعدادات التحديثات</h3>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-700">إيقاف التحديثات</span>
                        <div id="updatesToggle" class="toggle-switch" onclick="toggleUpdates()">
                            <div class="toggle-thumb"></div>
                        </div>
                    </div>
                    <p class="text-sm text-gray-500 mt-2" id="updatesStatus">التحديثات مفعلة</p>
                </div>
                
                <!-- Logout Button -->
                <button id="logoutBtn" class="w-full bg-red-500 text-white p-3 rounded-15 font-semibold hover:bg-red-600 transition-all duration-300">
                    🚪 تسجيل الخروج
                </button>
            </div>
        </div>
    </div>
    
    <!-- Speed Selection Modal -->
    <div id="speedModal" class="modal">
        <div class="modal-content">
            <h2 class="text-2xl font-bold text-center mb-6 text-gray-800">⚡ اختيار السرعة</h2>
            
            <div id="speedOptions" class="space-y-3">
                <div class="speed-card" data-speed="economy" data-name="سرعة اقتصادية">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="font-bold">🐌 سرعة اقتصادية</h3>
                            <p class="text-sm opacity-75">1 Mbps - توفير في استهلاك البيانات</p>
                        </div>
                        <div class="text-2xl">💰</div>
                    </div>
                </div>
                
                <div class="speed-card selected" data-speed="normal" data-name="سرعة عادية">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="font-bold">🚶 سرعة عادية</h3>
                            <p class="text-sm opacity-75">5 Mbps - مناسبة للاستخدام العادي</p>
                        </div>
                        <div class="text-2xl">✅</div>
                    </div>
                </div>
                
                <div class="speed-card" data-speed="medium" data-name="سرعة متوسطة">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="font-bold">🏃 سرعة متوسطة</h3>
                            <p class="text-sm opacity-75">10 Mbps - مناسبة لمشاهدة الفيديو</p>
                        </div>
                        <div class="text-2xl">📺</div>
                    </div>
                </div>
                
                <div class="speed-card" data-speed="high" data-name="سرعة عالية">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="font-bold">🏎️ سرعة عالية</h3>
                            <p class="text-sm opacity-75">25 Mbps - مناسبة للألعاب والتحميل</p>
                        </div>
                        <div class="text-2xl">🎮</div>
                    </div>
                </div>
                
                <div class="speed-card" data-speed="ultra" data-name="سرعة عالية جداً">
                    <div class="flex justify-between items-center">
                        <div>
                            <h3 class="font-bold">🚀 سرعة عالية جداً</h3>
                            <p class="text-sm opacity-75">50 Mbps - أقصى سرعة متاحة</p>
                        </div>
                        <div class="text-2xl">⚡</div>
                    </div>
                </div>
            </div>
            
            <!-- Updates Control in Modal -->
            <div class="mt-6 p-4 bg-gray-50 rounded-15">
                <div class="flex items-center justify-between mb-2">
                    <span class="font-semibold text-gray-700">📱 إيقاف التحديثات</span>
                    <div id="modalUpdatesToggle" class="toggle-switch" onclick="toggleModalUpdates()">
                        <div class="toggle-thumb"></div>
                    </div>
                </div>
                <p class="text-sm text-gray-500">منع تحديثات النظام والتطبيقات تلقائياً</p>
            </div>
            
            <div class="flex gap-3 mt-6">
                <button id="cancelSpeed" class="flex-1 bg-gray-300 text-gray-700 p-3 rounded-15 font-semibold hover:bg-gray-400 transition-all duration-300">
                    إلغاء
                </button>
                <button id="confirmSpeed" class="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 rounded-15 font-semibold hover:from-blue-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105">
                    <span id="confirmText">تأكيد الدخول</span>
                    <span id="confirmLoading" class="loading" style="display: none;"></span>
                </button>
            </div>
        </div>
    </div>
    
    <!-- Saved Cards Modal -->
    <div id="savedCardsModal" class="modal">
        <div class="modal-content">
            <h2 class="text-2xl font-bold text-center mb-6 text-gray-800">📋 الكروت المحفوظة</h2>
            
            <div id="savedCardsList" class="space-y-2 max-h-60 overflow-y-auto">
                <!-- Saved cards will be populated here -->
            </div>
            
            <div class="flex gap-3 mt-6">
                <button id="clearSavedCards" class="flex-1 bg-red-500 text-white p-3 rounded-15 font-semibold hover:bg-red-600 transition-all duration-300">
                    مسح الكل
                </button>
                <button id="closeSavedCards" class="flex-1 bg-gray-300 text-gray-700 p-3 rounded-15 font-semibold hover:bg-gray-400 transition-all duration-300">
                    إغلاق
                </button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let selectedSpeed = 'normal';
        let selectedSpeedName = 'سرعة عادية';
        let updatesBlocked = false;
        let connectionStartTime = null;
        let connectionTimer = null;
        let dataCounter = 0;
        let savedCards = [];
        let currentUser = '';
        
        // Ads rotation
        const adsMessages = [
            '🚀 اتصال سريع وآمن - استمتع بتجربة إنترنت مميزة!',
            '💎 خدمة إنترنت عالية الجودة - اشترك الآن!',
            '🌟 شبكة موثوقة وسريعة - للجميع!',
            '⚡ سرعات خيالية - تجربة لا تُنسى!',
            '🎯 الخيار الأمثل للإنترنت السريع!'
        ];
        
        let adsIndex = 0;
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadSavedCards();
            updateSavedCardsCount();
            startAdsRotation();
            
            // Check if user is already logged in
            const savedSession = getCookie('hotspot_session');
            if (savedSession) {
                try {
                    const sessionData = JSON.parse(savedSession);
                    currentUser = sessionData.username;
                    selectedSpeed = sessionData.speed || 'normal';
                    selectedSpeedName = sessionData.speedName || 'سرعة عادية';
                    updatesBlocked = sessionData.updatesBlocked || false;
                    showStatusPage();
                } catch (e) {
                    console.error('Error parsing session data:', e);
                }
            }
        });
        
        // Ads rotation function
        function startAdsRotation() {
            setInterval(() => {
                adsIndex = (adsIndex + 1) % adsMessages.length;
                const adsElement = document.getElementById('adsText');
                if (adsElement) {
                    adsElement.textContent = adsMessages[adsIndex];
                }
            }, 4000);
        }
        
        // Cookie functions
        function setCookie(name, value, days = 30) {
            const expires = new Date();
            expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
            document.cookie = `${name}=${encodeURIComponent(value)};expires=${expires.toUTCString()};path=/`;
        }
        
        function getCookie(name) {
            const nameEQ = name + "=";
            const ca = document.cookie.split(';');
            for(let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
            }
            return null;
        }
        
        // Load saved cards from localStorage
        function loadSavedCards() {
            try {
                const saved = localStorage.getItem('hotspot_saved_cards');
                if (saved) {
                    savedCards = JSON.parse(saved);
                }
            } catch (e) {
                console.error('Error loading saved cards:', e);
                savedCards = [];
            }
        }
        
        // Save cards to localStorage
        function saveSavedCards() {
            try {
                localStorage.setItem('hotspot_saved_cards', JSON.stringify(savedCards));
            } catch (e) {
                console.error('Error saving cards:', e);
            }
        }
        
        // Add card to saved list
        function addToSavedCards(username) {
            if (!username) return;
            
            // Remove if already exists
            savedCards = savedCards.filter(card => card.username !== username);
            
            // Add to beginning
            savedCards.unshift({
                username: username,
                date: new Date().toLocaleDateString('ar-SA'),
                time: new Date().toLocaleTimeString('ar-SA')
            });
            
            // Keep only last 5
            if (savedCards.length > 5) {
                savedCards = savedCards.slice(0, 5);
            }
            
            saveSavedCards();
            updateSavedCardsCount();
        }
        
        // Update saved cards count
        function updateSavedCardsCount() {
            const countElement = document.getElementById('savedCount');
            if (countElement) {
                countElement.textContent = savedCards.length;
            }
        }
        
        // Enhanced form validation
        function validateUsername(username) {
            if (!username || username.trim().length === 0) {
                return 'يرجى إدخال اسم المستخدم';
            }
            
            const trimmed = username.trim();
            const minLength = 3;
            const maxLength = 20;
            
            if (trimmed.length < minLength) {
                return `اسم المستخدم يجب أن يكون ${minLength} أحرف على الأقل`;
            }
            
            if (trimmed.length > maxLength) {
                return `اسم المستخدم يجب أن يكون ${maxLength} حرف كحد أقصى`;
            }
            
            return null;
        }
        
        // Login form submission
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            
            // Enhanced validation
            const validationError = validateUsername(username);
            if (validationError) {
                showNotification(validationError, 'error');
                return;
            }
            
            currentUser = username;
            
            // Show loading
            const loginText = document.getElementById('loginText');
            const loginLoading = document.getElementById('loginLoading');
            const submitBtn = e.target.querySelector('button[type="submit"]');
            
            if (loginText && loginLoading && submitBtn) {
                loginText.style.display = 'none';
                loginLoading.style.display = 'inline-block';
                submitBtn.disabled = true;
                
                // Simulate login delay
                setTimeout(() => {
                    // Hide loading
                    loginText.style.display = 'inline';
                    loginLoading.style.display = 'none';
                    submitBtn.disabled = false;
                    
                    // Show speed selection modal
                    showSpeedModal();
                }, 1500);
            }
        });
        
        // Show speed selection modal
        function showSpeedModal() {
            const modal = document.getElementById('speedModal');
            if (modal) {
                modal.classList.add('show');
                
                // Update modal updates toggle
                const modalToggle = document.getElementById('modalUpdatesToggle');
                if (modalToggle) {
                    if (updatesBlocked) {
                        modalToggle.classList.add('active');
                    } else {
                        modalToggle.classList.remove('active');
                    }
                }
            }
        }
        
        // Speed card selection
        const speedOptions = document.getElementById('speedOptions');
        if (speedOptions) {
            speedOptions.addEventListener('click', function(e) {
                const card = e.target.closest('.speed-card');
                if (card) {
                    // Remove selection from all cards
                    document.querySelectorAll('.speed-card').forEach(c => c.classList.remove('selected'));
                    
                    // Add selection to clicked card
                    card.classList.add('selected');
                    
                    selectedSpeed = card.dataset.speed;
                    selectedSpeedName = card.dataset.name;
                }
            });
        }
        
        // Toggle updates in modal
        function toggleModalUpdates() {
            const toggle = document.getElementById('modalUpdatesToggle');
            if (toggle) {
                updatesBlocked = !updatesBlocked;
                
                if (updatesBlocked) {
                    toggle.classList.add('active');
                } else {
                    toggle.classList.remove('active');
                }
            }
        }
        
        // Confirm speed selection
        const confirmSpeedBtn = document.getElementById('confirmSpeed');
        if (confirmSpeedBtn) {
            confirmSpeedBtn.addEventListener('click', function() {
                const confirmText = document.getElementById('confirmText');
                const confirmLoading = document.getElementById('confirmLoading');
                
                if (confirmText && confirmLoading) {
                    // Show loading state
                    confirmText.style.display = 'none';
                    confirmLoading.style.display = 'inline-block';
                    
                    // Disable button during loading
                    this.disabled = true;
                    this.style.opacity = '0.7';
                    
                    // Simulate connection process
                    setTimeout(() => {
                        // Add to saved cards
                        addToSavedCards(currentUser);
                        
                        // Save session
                        const sessionData = {
                            username: currentUser,
                            speed: selectedSpeed,
                            speedName: selectedSpeedName,
                            updatesBlocked: updatesBlocked,
                            loginTime: new Date().toISOString()
                        };
                        setCookie('hotspot_session', JSON.stringify(sessionData));
                        
                        // Show success notification
                        showNotification('تم تسجيل الدخول بنجاح! 🎉', 'success');
                        
                        // Hide modal and show status page
                        const modal = document.getElementById('speedModal');
                        if (modal) {
                            modal.classList.remove('show');
                        }
                        
                        // Reset button state
                        confirmText.style.display = 'inline';
                        confirmLoading.style.display = 'none';
                        this.disabled = false;
                        this.style.opacity = '1';
                        
                        // Show status page with animation
                        setTimeout(() => {
                            showStatusPage();
                        }, 300);
                        
                    }, 2000);
                }
            });
        }
        
        // Cancel speed selection
        const cancelSpeedBtn = document.getElementById('cancelSpeed');
        if (cancelSpeedBtn) {
            cancelSpeedBtn.addEventListener('click', function() {
                const modal = document.getElementById('speedModal');
                if (modal) {
                    modal.classList.remove('show');
                }
            });
        }
        
        // Show status page
        function showStatusPage() {
            const loginPage = document.getElementById('loginPage');
            const statusPage = document.getElementById('statusPage');
            
            if (loginPage && statusPage) {
                // Hide login page with fade out
                loginPage.style.opacity = '0';
                loginPage.style.transform = 'translateX(-100%)';
                
                setTimeout(() => {
                    loginPage.style.display = 'none';
                    
                    // Show status page with fade in
                    statusPage.style.display = 'block';
                    statusPage.style.opacity = '0';
                    statusPage.style.transform = 'translateX(100%)';
                    
                    // Animate in
                    setTimeout(() => {
                        statusPage.style.opacity = '1';
                        statusPage.style.transform = 'translateX(0)';
                    }, 50);
                    
                }, 300);
            }
            
            // Update status page
            const connectedUserElement = document.getElementById('connectedUser');
            const currentSpeedElement = document.getElementById('currentSpeed');
            
            if (connectedUserElement) {
                connectedUserElement.textContent = currentUser;
            }
            if (currentSpeedElement) {
                currentSpeedElement.textContent = selectedSpeedName;
            }
            
            // Update updates toggle
            const statusToggle = document.getElementById('updatesToggle');
            const statusText = document.getElementById('updatesStatus');
            
            if (statusToggle && statusText) {
                if (updatesBlocked) {
                    statusToggle.classList.add('active');
                    statusText.textContent = 'التحديثات متوقفة';
                    statusText.className = 'text-sm text-red-500 mt-2';
                } else {
                    statusToggle.classList.remove('active');
                    statusText.textContent = 'التحديثات مفعلة';
                    statusText.className = 'text-sm text-green-500 mt-2';
                }
            }
            
            // Start connection timer and monitoring
            startConnectionTimer();
            startDataCounter();
            startConnectionMonitoring();
            
            // Show welcome message
            setTimeout(() => {
                showNotification(`مرحباً ${currentUser}! تم الاتصال بسرعة ${selectedSpeedName}`, 'success');
            }, 1000);
        }
        
        // Start connection timer
        function startConnectionTimer() {
            // Clear existing timer
            if (connectionTimer) {
                clearInterval(connectionTimer);
            }
            
            connectionStartTime = new Date();
            connectionTimer = setInterval(() => {
                const now = new Date();
                const diff = now - connectionStartTime;
                
                const hours = Math.floor(diff / (1000 * 60 * 60));
                const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((diff % (1000 * 60)) / 1000);
                
                const connectionTimeElement = document.getElementById('connectionTime');
                if (connectionTimeElement) {
                    connectionTimeElement.textContent = 
                        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                }
            }, 1000);
        }
        
        // Start data counter
        function startDataCounter() {
            setInterval(() => {
                if (document.getElementById('statusPage') && document.getElementById('statusPage').style.display !== 'none') {
                    // Simulate data usage based on speed
                    let increment = 0.1; // Base increment in MB
                    
                    switch(selectedSpeed) {
                        case 'economy': increment = 0.05; break;
                        case 'normal': increment = 0.1; break;
                        case 'medium': increment = 0.2; break;
                        case 'high': increment = 0.5; break;
                        case 'ultra': increment = 1.0; break;
                    }
                    
                    dataCounter += increment;
                    
                    const dataUsedElement = document.getElementById('dataUsed');
                    if (dataUsedElement) {
                        if (dataCounter < 1024) {
                            dataUsedElement.textContent = `${dataCounter.toFixed(1)} MB`;
                        } else {
                            dataUsedElement.textContent = `${(dataCounter / 1024).toFixed(2)} GB`;
                        }
                    }
                }
            }, 1000);
        }
        
        // Toggle updates in status page
        function toggleUpdates() {
            const toggle = document.getElementById('updatesToggle');
            const statusText = document.getElementById('updatesStatus');
            
            if (toggle && statusText) {
                updatesBlocked = !updatesBlocked;
                
                if (updatesBlocked) {
                    toggle.classList.add('active');
                    statusText.textContent = 'التحديثات متوقفة';
                    statusText.className = 'text-sm text-red-500 mt-2';
                } else {
                    toggle.classList.remove('active');
                    statusText.textContent = 'التحديثات مفعلة';
                    statusText.className = 'text-sm text-green-500 mt-2';
                }
                
                // Update session cookie
                const sessionData = {
                    username: currentUser,
                    speed: selectedSpeed,
                    speedName: selectedSpeedName,
                    updatesBlocked: updatesBlocked,
                    loginTime: new Date().toISOString()
                };
                setCookie('hotspot_session', JSON.stringify(sessionData));
            }
        }
        
        // Change speed from status page
        const changeSpeedBtn = document.getElementById('changeSpeedBtn');
        if (changeSpeedBtn) {
            changeSpeedBtn.addEventListener('click', function() {
                showSpeedModal();
            });
        }
        
        // Show saved cards modal
        const showSavedCardsBtn = document.getElementById('showSavedCards');
        if (showSavedCardsBtn) {
            showSavedCardsBtn.addEventListener('click', function() {
                showSavedCardsModal();
            });
        }
        
        // Show saved cards modal
        function showSavedCardsModal() {
            const modal = document.getElementById('savedCardsModal');
            const list = document.getElementById('savedCardsList');
            
            if (modal && list) {
                // Clear list
                list.innerHTML = '';
                
                if (savedCards.length === 0) {
                    list.innerHTML = '<p class="text-center text-gray-500 py-8">لا توجد كروت محفوظة</p>';
                } else {
                    savedCards.forEach((card, index) => {
                        const cardElement = document.createElement('div');
                        cardElement.className = 'saved-card';
                        cardElement.innerHTML = `
                            <div>
                                <div class="font-semibold">${card.username}</div>
                                <div class="text-sm text-gray-500">${card.date} - ${card.time}</div>
                            </div>
                            <button onclick="useSavedCard('${card.username}')" class="bg-blue-500 text-white px-3 py-1 rounded-lg text-sm hover:bg-blue-600 transition-colors">
                                استخدام
                            </button>
                        `;
                        list.appendChild(cardElement);
                    });
                }
                
                modal.classList.add('show');
            }
        }
        
        // Use saved card
        function useSavedCard(username) {
            const usernameInput = document.getElementById('username');
            const modal = document.getElementById('savedCardsModal');
            
            if (usernameInput) {
                usernameInput.value = username;
            }
            if (modal) {
                modal.classList.remove('show');
            }
        }
        
        // Clear saved cards
        const clearSavedCardsBtn = document.getElementById('clearSavedCards');
        if (clearSavedCardsBtn) {
            clearSavedCardsBtn.addEventListener('click', function() {
                if (confirm('هل أنت متأكد من مسح جميع الكروت المحفوظة؟')) {
                    savedCards = [];
                    saveSavedCards();
                    updateSavedCardsCount();
                    showSavedCardsModal(); // Refresh the modal
                }
            });
        }
        
        // Close saved cards modal
        const closeSavedCardsBtn = document.getElementById('closeSavedCards');
        if (closeSavedCardsBtn) {
            closeSavedCardsBtn.addEventListener('click', function() {
                const modal = document.getElementById('savedCardsModal');
                if (modal) {
                    modal.classList.remove('show');
                }
            });
        }
        
        // Logout
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', function() {
                if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                    // Clear session
                    setCookie('hotspot_session', '', -1);
                    
                    // Clear timers
                    if (connectionTimer) {
                        clearInterval(connectionTimer);
                    }
                    
                    // Reset variables
                    currentUser = '';
                    selectedSpeed = 'normal';
                    selectedSpeedName = 'سرعة عادية';
                    updatesBlocked = false;
                    dataCounter = 0;
                    
                    // Show login page
                    const statusPage = document.getElementById('statusPage');
                    const loginPage = document.getElementById('loginPage');
                    
                    if (statusPage && loginPage) {
                        statusPage.style.display = 'none';
                        loginPage.style.display = 'block';
                        loginPage.style.opacity = '1';
                        loginPage.style.transform = 'translateX(0)';
                    }
                    
                    // Reset form
                    const usernameInput = document.getElementById('username');
                    if (usernameInput) {
                        usernameInput.value = '';
                    }
                    
                    showNotification('تم تسجيل الخروج بنجاح', 'success');
                }
            });
        }
        
        // Close modals when clicking outside
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal')) {
                e.target.classList.remove('show');
            }
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Escape to close modals
            if (e.key === 'Escape') {
                document.querySelectorAll('.modal.show').forEach(modal => {
                    modal.classList.remove('show');
                });
            }
        });
        
        // Network quality and bandwidth monitoring
        function updateNetworkQuality() {
            const qualities = ['ممتاز', 'جيد جداً', 'جيد', 'متوسط'];
            const colors = ['text-green-600', 'text-blue-600', 'text-yellow-600', 'text-orange-600'];
            const randomIndex = Math.floor(Math.random() * qualities.length);
            
            const qualityElement = document.getElementById('networkQuality');
            if (qualityElement) {
                qualityElement.textContent = qualities[randomIndex];
                qualityElement.className = `font-semibold ${colors[randomIndex]}`;
            }
        }
        
        function updateBandwidthChart() {
            const chart = document.getElementById('bandwidthChart');
            if (chart) {
                const usage = Math.floor(Math.random() * 100);
                chart.style.width = usage + '%';
                const percentElement = chart.parentElement.querySelector('.usage-percent');
                if (percentElement) {
                    percentElement.textContent = usage + '%';
                }
            }
        }
        
        // Connection monitoring
        function monitorConnection() {
            const connectionStates = ['stable', 'slow', 'unstable'];
            const currentState = connectionStates[Math.floor(Math.random() * connectionStates.length)];
            
            const statusIndicator = document.getElementById('connectionIndicator');
            if (statusIndicator) {
                switch(currentState) {
                    case 'stable':
                        statusIndicator.className = 'w-3 h-3 bg-green-500 rounded-full animate-pulse';
                        break;
                    case 'slow':
                        statusIndicator.className = 'w-3 h-3 bg-yellow-500 rounded-full animate-pulse';
                        break;
                    case 'unstable':
                        statusIndicator.className = 'w-3 h-3 bg-red-500 rounded-full animate-pulse';
                        break;
                }
            }
        }
        
        let connectionMonitor;
        function startConnectionMonitoring() {
            connectionMonitor = setInterval(() => {
                if (document.getElementById('statusPage') && document.getElementById('statusPage').style.display !== 'none') {
                    updateNetworkQuality();
                    updateBandwidthChart();
                    monitorConnection();
                }
            }, 5000);
        }
        
        // Enhanced notification system
        function showNotification(message, type = 'info') {
            // Remove existing notifications
            document.querySelectorAll('.notification').forEach(n => n.remove());
            
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            
            const bgColor = {
                'success': 'bg-green-500',
                'error': 'bg-red-500',
                'warning': 'bg-yellow-500',
                'info': 'bg-blue-500'
            }[type] || 'bg-blue-500';
            
            notification.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="mr-2 text-white hover:text-gray-200 text-lg">
                        ×
                    </button>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Animate in
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 300);
            }, 5000);
        }
        
        // Initialize
        console.log('🚀 MikroTik Hotspot System Initialized Successfully');
    </script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'978ddb00a4cf9a0c',t:'MTc1NjgyNTAwMS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>
</html>
